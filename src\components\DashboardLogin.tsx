import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface DashboardLoginProps {
    onLogin: () => void;
}

const DashboardLogin: React.FC<DashboardLoginProps> = ({ onLogin }) => {
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');

        // Simulate authentication delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (password === 'aboy101') {
            onLogin();
        } else {
            setError('Invalid password. Please try again.');
        }
        
        setIsLoading(false);
    };

    return (
        <div style={{
            minHeight: '100vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: '#f9fafb',
            padding: 'clamp(16px, 4vw, 32px)'
        }}>
            <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3 }}
                style={{
                    backgroundColor: '#ffffff',
                    padding: 'clamp(32px, 6vw, 48px)',
                    borderRadius: '16px',
                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    border: '1px solid #e5e7eb',
                    width: '100%',
                    maxWidth: '400px'
                }}
            >
                {/* Header */}
                <div style={{
                    textAlign: 'center',
                    marginBottom: '32px'
                }}>
                    <div style={{
                        fontSize: '48px',
                        marginBottom: '16px'
                    }}>
                        🔐
                    </div>
                    <h1 style={{
                        fontSize: 'clamp(20px, 5vw, 28px)',
                        fontWeight: '700',
                        color: '#111827',
                        margin: '0 0 8px 0',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                    }}>
                        Dashboard Access
                    </h1>
                    <p style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        margin: '0',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                    }}>
                        Enter password to access payment dashboard
                    </p>
                </div>

                {/* Login Form */}
                <form onSubmit={handleSubmit}>
                    <div style={{ marginBottom: '24px' }}>
                        <label htmlFor="password" style={{
                            display: 'block',
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#374151',
                            marginBottom: '8px',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            Password
                        </label>
                        <input
                            type="password"
                            id="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="Enter dashboard password"
                            required
                            style={{
                                width: '100%',
                                padding: '16px 18px',
                                borderRadius: '8px',
                                border: '1px solid #e5e7eb',
                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                outline: 'none',
                                fontSize: '16px',
                                backgroundColor: '#ffffff',
                                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                                boxSizing: 'border-box'
                            }}
                            onFocus={(e) => {
                                e.target.style.borderColor = '#3b82f6';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.transform = 'translateY(0)';
                            }}
                        />
                    </div>

                    {error && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            style={{
                                backgroundColor: '#fef2f2',
                                border: '1px solid #fecaca',
                                borderRadius: '8px',
                                padding: '12px 16px',
                                marginBottom: '24px',
                                color: '#dc2626',
                                fontSize: '14px',
                                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            }}
                        >
                            🚫 {error}
                        </motion.div>
                    )}

                    <button
                        type="submit"
                        disabled={isLoading}
                        style={{
                            width: '100%',
                            padding: '16px',
                            borderRadius: '8px',
                            border: 'none',
                            backgroundColor: isLoading ? '#9ca3af' : '#10b981',
                            color: '#ffffff',
                            fontSize: '16px',
                            fontWeight: '600',
                            cursor: isLoading ? 'not-allowed' : 'pointer',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                            transition: 'all 0.2s ease',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '8px'
                        }}
                        onMouseEnter={(e) => {
                            if (!isLoading) {
                                (e.target as HTMLButtonElement).style.backgroundColor = '#059669';
                                (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!isLoading) {
                                (e.target as HTMLButtonElement).style.backgroundColor = '#10b981';
                                (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
                            }
                        }}
                    >
                        {isLoading ? (
                            <>
                                <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                    style={{
                                        width: '16px',
                                        height: '16px',
                                        borderRadius: '50%',
                                        border: '2px solid #ffffff',
                                        borderTopColor: 'transparent'
                                    }}
                                />
                                Authenticating...
                            </>
                        ) : (
                            <>
                                🔓 Access Dashboard
                            </>
                        )}
                    </button>
                </form>

                {/* Back to Payment Form */}
                <div style={{
                    marginTop: '24px',
                    textAlign: 'center'
                }}>
                    <button
                        onClick={() => window.location.href = '/'}
                        style={{
                            background: 'none',
                            border: 'none',
                            color: '#6b7280',
                            fontSize: '14px',
                            cursor: 'pointer',
                            textDecoration: 'underline',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}
                        onMouseEnter={(e) => {
                            (e.target as HTMLButtonElement).style.color = '#10b981';
                        }}
                        onMouseLeave={(e) => {
                            (e.target as HTMLButtonElement).style.color = '#6b7280';
                        }}
                    >
                        ← Back to Payment Form
                    </button>
                </div>
            </motion.div>
        </div>
    );
};

export default DashboardLogin;
