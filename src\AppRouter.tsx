import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Auth from './components/Auth';
import PaymentDashboard from './components/PaymentDashboard';
import SendPayment from './components/SendPayment';
import TransactionHistory from './components/TransactionHistory';

const AppRouter: React.FC = () => {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<Auth />} />
                <Route path="/dashboard" element={<PaymentDashboard />} />
                <Route path="/send-payment" element={<SendPayment />} />
                <Route path="/transaction-history" element={<TransactionHistory />} />
            </Routes>
        </Router>
    );
};

export default AppRouter;
