import React from 'react';

const Dashboard: React.FC = () => {
    const userBalance = 5000; // Mock balance
    const recentTransactions = [
        { id: 1, description: 'Payment to <PERSON>', amount: -200, date: '2025-08-10' },
        { id: 2, description: 'Payment from <PERSON>', amount: 300, date: '2025-08-09' },
        { id: 3, description: 'Payment to Grocery Store', amount: -50, date: '2025-08-08' },
    ];

    return (
        <div style={{ padding: '20px' }}>
            <h1>Dashboard</h1>
            <div style={{ marginBottom: '20px' }}>
                <h2>Balance: ${userBalance}</h2>
            </div>
            <div style={{ marginBottom: '20px' }}>
                <h3>Recent Transactions</h3>
                <ul>
                    {recentTransactions.map((transaction) => (
                        <li key={transaction.id}>
                            {transaction.description} - ${transaction.amount} on {transaction.date}
                        </li>
                    ))}
                </ul>
            </div>
            <div>
                <button style={{ marginRight: '10px' }}>Send Payment</button>
                <button>View History</button>
            </div>
        </div>
    );
};

export default Dashboard;
