import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import DashboardLogin from './DashboardLogin';
import PaymentDetails from './PaymentDetails';

interface PaymentData {
    id: string;
    email: string;
    amount: string;
    currency: string;
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    nameOnCard: string;
    country: string;
    timestamp: string;
    formattedAmount: string;
}

// Mock data for demonstration (in production, this would fetch from Netlify API)
const mockPayments: PaymentData[] = [
    {
        id: '1',
        email: '<EMAIL>',
        amount: '25.00',
        currency: 'USD',
        cardNumber: '****1234',
        expiryDate: '12/25',
        cvv: '123',
        nameOnCard: '<PERSON>',
        country: 'United States',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
        formattedAmount: '$25.00'
    },
    {
        id: '2',
        email: '<EMAIL>',
        amount: '50.00',
        currency: 'EUR',
        cardNumber: '****5678',
        expiryDate: '08/26',
        cvv: '456',
        nameOnCard: '<PERSON>',
        country: 'Germany',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        formattedAmount: '€50.00'
    },
    {
        id: '3',
        email: '<EMAIL>',
        amount: '75.99',
        currency: 'GBP',
        cardNumber: '****9012',
        expiryDate: '03/27',
        cvv: '789',
        nameOnCard: 'Bob Wilson',
        country: 'United Kingdom',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        formattedAmount: '£75.99'
    },
    {
        id: '4',
        email: '<EMAIL>',
        amount: '120.50',
        currency: 'USD',
        cardNumber: '****3456',
        expiryDate: '11/24',
        cvv: '321',
        nameOnCard: 'Alice Johnson',
        country: 'Canada',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        formattedAmount: '$120.50'
    }
];

const PaymentDashboard: React.FC = () => {
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [payments, setPayments] = useState<PaymentData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedPayment, setSelectedPayment] = useState<PaymentData | null>(null);

    useEffect(() => {
        // Simulate API call
        const fetchPayments = async () => {
            try {
                setLoading(true);
                // In production, you would fetch from Netlify Forms API
                // const response = await fetch('/.netlify/functions/get-form-submissions');
                // const data = await response.json();

                // For now, use mock data
                await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate loading
                setPayments(mockPayments);
            } catch (err) {
                setError('Failed to fetch payment data');
            } finally {
                setLoading(false);
            }
        };

        fetchPayments();

        // Set up polling for real-time updates (every 30 seconds)
        const interval = setInterval(fetchPayments, 30000);

        return () => clearInterval(interval);
    }, []); // Empty dependency array is fine since mockPayments is constant

    const formatDate = (timestamp: string) => {
        return new Date(timestamp).toLocaleString();
    };

    const getStatusColor = (timestamp: string) => {
        const now = new Date();
        const paymentTime = new Date(timestamp);
        const diffMinutes = (now.getTime() - paymentTime.getTime()) / (1000 * 60);

        if (diffMinutes < 5) return '#10b981'; // Green for recent
        if (diffMinutes < 60) return '#f59e0b'; // Yellow for within hour
        return '#6b7280'; // Gray for older
    };

    const getStatusText = (timestamp: string) => {
        const now = new Date();
        const paymentTime = new Date(timestamp);
        const diffMinutes = (now.getTime() - paymentTime.getTime()) / (1000 * 60);

        if (diffMinutes < 5) return 'Just now';
        if (diffMinutes < 60) return 'Recent';
        return 'Processed';
    };

    // Show login screen if not authenticated
    if (!isAuthenticated) {
        return <DashboardLogin onLogin={() => setIsAuthenticated(true)} />;
    }

    if (loading) {
        return (
            <div style={{
                minHeight: '100vh',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#f9fafb'
            }}>
                <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    style={{
                        width: '50px',
                        height: '50px',
                        borderRadius: '50%',
                        border: '3px solid #10b981',
                        borderTopColor: 'transparent'
                    }}
                />
            </div>
        );
    }

    if (error) {
        return (
            <div style={{
                minHeight: '100vh',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#f9fafb'
            }}>
                <div style={{
                    textAlign: 'center',
                    color: '#ef4444',
                    fontSize: '18px'
                }}>
                    {error}
                </div>
            </div>
        );
    }

    return (
        <div style={{
            minHeight: '100vh',
            backgroundColor: '#f9fafb',
            padding: 'clamp(16px, 4vw, 32px)'
        }}>
            <div style={{
                maxWidth: '1200px',
                margin: '0 auto'
            }}>
                {/* Header */}
                <motion.div
                    initial={{ y: -20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5 }}
                    style={{
                        marginBottom: '32px',
                        textAlign: 'center'
                    }}
                >
                    <h1 style={{
                        fontSize: 'clamp(24px, 5vw, 36px)',
                        fontWeight: '700',
                        color: '#111827',
                        margin: '0 0 8px 0',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                    }}>
                        💳 Payment Dashboard
                    </h1>
                    <p style={{
                        fontSize: '16px',
                        color: '#6b7280',
                        margin: '0',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                    }}>
                        Real-time payment submissions from Netlify Forms
                    </p>
                    <div style={{
                        marginTop: '16px',
                        display: 'inline-flex',
                        alignItems: 'center',
                        backgroundColor: '#10b981',
                        color: 'white',
                        padding: '4px 12px',
                        borderRadius: '20px',
                        fontSize: '12px',
                        fontWeight: '600'
                    }}>
                        🟢 Live Updates Every 30s
                    </div>
                </motion.div>

                {/* Stats Cards */}
                <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                        gap: '20px',
                        marginBottom: '32px'
                    }}
                >
                    <div style={{
                        backgroundColor: '#ffffff',
                        padding: '24px',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <h3 style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#6b7280',
                            margin: '0 0 8px 0',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em'
                        }}>
                            📊 Total Payments
                        </h3>
                        <p style={{
                            fontSize: '32px',
                            fontWeight: '700',
                            color: '#111827',
                            margin: '0'
                        }}>
                            {payments.length}
                        </p>
                    </div>

                    <div style={{
                        backgroundColor: '#ffffff',
                        padding: '24px',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <h3 style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#6b7280',
                            margin: '0 0 8px 0',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em'
                        }}>
                            💰 Total Revenue
                        </h3>
                        <p style={{
                            fontSize: '32px',
                            fontWeight: '700',
                            color: '#10b981',
                            margin: '0'
                        }}>
                            ${payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0).toFixed(2)}
                        </p>
                    </div>

                    <div style={{
                        backgroundColor: '#ffffff',
                        padding: '24px',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <h3 style={{
                            fontSize: '14px',
                            fontWeight: '600',
                            color: '#6b7280',
                            margin: '0 0 8px 0',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em'
                        }}>
                            ⚡ Recent (1h)
                        </h3>
                        <p style={{
                            fontSize: '32px',
                            fontWeight: '700',
                            color: '#f59e0b',
                            margin: '0'
                        }}>
                            {payments.filter(p => {
                                const diffMinutes = (new Date().getTime() - new Date(p.timestamp).getTime()) / (1000 * 60);
                                return diffMinutes < 60;
                            }).length}
                        </p>
                    </div>
                </motion.div>

                {/* Payments Table */}
                <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    style={{
                        backgroundColor: '#ffffff',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb',
                        overflow: 'hidden'
                    }}
                >
                    <div style={{
                        padding: '24px',
                        borderBottom: '1px solid #e5e7eb'
                    }}>
                        <h2 style={{
                            fontSize: '18px',
                            fontWeight: '600',
                            color: '#111827',
                            margin: '0',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            💳 Payment Submissions
                        </h2>
                        <p style={{
                            fontSize: '14px',
                            color: '#6b7280',
                            margin: '8px 0 0 0',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            Click on any payment to view full details
                        </p>
                    </div>

                    <div style={{ overflowX: 'auto' }}>
                        <table style={{
                            width: '100%',
                            borderCollapse: 'collapse'
                        }}>
                            <thead>
                                <tr style={{ backgroundColor: '#f9fafb' }}>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Status
                                    </th>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Email
                                    </th>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Amount
                                    </th>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Card
                                    </th>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Name
                                    </th>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Country
                                    </th>
                                    <th style={{
                                        padding: '12px 24px',
                                        textAlign: 'left',
                                        fontSize: '12px',
                                        fontWeight: '600',
                                        color: '#6b7280',
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.05em'
                                    }}>
                                        Date
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {payments.map((payment, index) => (
                                    <motion.tr
                                        key={payment.id}
                                        initial={{ x: -20, opacity: 0 }}
                                        animate={{ x: 0, opacity: 1 }}
                                        transition={{ duration: 0.3, delay: index * 0.1 }}
                                        style={{
                                            borderBottom: '1px solid #f3f4f6',
                                            cursor: 'pointer',
                                            transition: 'background-color 0.2s ease'
                                        }}
                                        onClick={() => setSelectedPayment(payment)}
                                        onMouseEnter={(e) => {
                                            (e.currentTarget as HTMLTableRowElement).style.backgroundColor = '#f9fafb';
                                        }}
                                        onMouseLeave={(e) => {
                                            (e.currentTarget as HTMLTableRowElement).style.backgroundColor = 'transparent';
                                        }}
                                    >
                                        <td style={{ padding: '16px 24px' }}>
                                            <div style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '8px'
                                            }}>
                                                <div style={{
                                                    width: '8px',
                                                    height: '8px',
                                                    borderRadius: '50%',
                                                    backgroundColor: getStatusColor(payment.timestamp)
                                                }} />
                                                <span style={{
                                                    fontSize: '12px',
                                                    color: getStatusColor(payment.timestamp),
                                                    fontWeight: '600'
                                                }}>
                                                    {getStatusText(payment.timestamp)}
                                                </span>
                                            </div>
                                        </td>
                                        <td style={{
                                            padding: '16px 24px',
                                            fontSize: '14px',
                                            color: '#111827'
                                        }}>
                                            {payment.email || 'Not provided'}
                                        </td>
                                        <td style={{
                                            padding: '16px 24px',
                                            fontSize: '14px',
                                            fontWeight: '600',
                                            color: '#111827'
                                        }}>
                                            {payment.formattedAmount}
                                        </td>
                                        <td style={{
                                            padding: '16px 24px',
                                            fontSize: '14px',
                                            color: '#6b7280'
                                        }}>
                                            {payment.cardNumber}
                                        </td>
                                        <td style={{
                                            padding: '16px 24px',
                                            fontSize: '14px',
                                            color: '#111827'
                                        }}>
                                            {payment.nameOnCard}
                                        </td>
                                        <td style={{
                                            padding: '16px 24px',
                                            fontSize: '14px',
                                            color: '#6b7280'
                                        }}>
                                            {payment.country}
                                        </td>
                                        <td style={{
                                            padding: '16px 24px',
                                            fontSize: '14px',
                                            color: '#6b7280'
                                        }}>
                                            {formatDate(payment.timestamp)}
                                        </td>
                                    </motion.tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {payments.length === 0 && (
                        <div style={{
                            padding: '48px 24px',
                            textAlign: 'center',
                            color: '#6b7280'
                        }}>
                            No payment submissions yet.
                        </div>
                    )}
                </motion.div>

                {/* Back to Payment Form Button */}
                <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    style={{
                        marginTop: '32px',
                        textAlign: 'center'
                    }}
                >
                    <button
                        onClick={() => window.location.href = '/'}
                        style={{
                            padding: '12px 24px',
                            borderRadius: '8px',
                            border: 'none',
                            backgroundColor: '#10b981',
                            color: '#ffffff',
                            fontSize: '16px',
                            fontWeight: '600',
                            cursor: 'pointer',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                            transition: 'all 0.2s ease'
                        }}
                        onMouseEnter={(e) => {
                            (e.target as HTMLButtonElement).style.backgroundColor = '#059669';
                            (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
                        }}
                        onMouseLeave={(e) => {
                            (e.target as HTMLButtonElement).style.backgroundColor = '#10b981';
                            (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
                        }}
                    >
                        ← Back to Payment Form
                    </button>
                </motion.div>

                {/* Payment Details Modal */}
                <AnimatePresence>
                    {selectedPayment && (
                        <PaymentDetails
                            payment={selectedPayment}
                            onClose={() => setSelectedPayment(null)}
                        />
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
};

export default PaymentDashboard;
