import React, { useState } from 'react';
import { motion } from 'framer-motion';

const PaymentForm: React.FC = () => {
    const [email, setEmail] = useState('');
    const [amount, setAmount] = useState('');
    const [cardNumber, setCardNumber] = useState('');
    const [expiryDate, setExpiryDate] = useState('');
    const [cvv, setCvv] = useState('');
    const [nameOnCard, setNameOnCard] = useState('');
    const [country, setCountry] = useState('United States');

    const inputStyle = {
        width: '100%',
        padding: '14px 16px',
        borderRadius: '6px',
        border: '1px solid #d1d5db',
        transition: 'all 0.2s ease',
        outline: 'none',
        fontSize: '16px',
        backgroundColor: '#ffffff',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        boxSizing: 'border-box' as const
    };

    const labelStyle = {
        display: 'block',
        marginBottom: '6px',
        fontWeight: '500',
        color: '#374151',
        fontSize: '14px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        console.log('Payment Details:', { email, amount, cardNumber, expiryDate, cvv, nameOnCard, country });
        // Add logic to process the payment
    };

    return (
        <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            style={{
                maxWidth: '400px',
                margin: '0 auto',
                padding: '32px',
                textAlign: 'left',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                borderRadius: '12px',
                backgroundColor: '#ffffff',
                border: '1px solid #e5e7eb'
            }}>

            {/* Heading */}
            <motion.div
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                style={{
                    marginBottom: '32px',
                    textAlign: 'center'
                }}
            >
                <h1 style={{
                    fontSize: '24px',
                    fontWeight: '600',
                    color: '#111827',
                    margin: '0 0 8px 0',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                }}>
                    Complete Payment
                </h1>
                <p style={{
                    fontSize: '16px',
                    color: '#6b7280',
                    margin: '0',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                }}>
                    Enter your payment details below
                </p>
            </motion.div>
            <motion.form
                onSubmit={handleSubmit}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
            >
                {/* Email Field */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="email" style={labelStyle}>
                        Email
                    </label>
                    <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                        placeholder="<EMAIL>"
                        required
                        style={inputStyle}
                    />
                </motion.div>

                {/* Amount Field */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="amount" style={labelStyle}>
                        Amount
                    </label>
                    <div style={{ position: 'relative' }}>
                        <span style={{
                            position: 'absolute',
                            left: '16px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            color: '#6b7280',
                            fontSize: '16px',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            $
                        </span>
                        <input
                            type="number"
                            id="amount"
                            value={amount}
                            onChange={(e) => setAmount(e.target.value)}
                            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                            placeholder="0.00"
                            required
                            min="0"
                            step="0.01"
                            style={{
                                ...inputStyle,
                                paddingLeft: '32px'
                            }}
                        />
                    </div>
                </motion.div>
                {/* Card Information Section */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="cardNumber" style={labelStyle}>Card information</label>
                    <div style={{ position: 'relative' }}>
                        <input
                            type="text"
                            id="cardNumber"
                            value={cardNumber}
                            onChange={(e) => setCardNumber(e.target.value)}
                            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                            placeholder="1234 1234 1234 1234"
                            required
                            style={{
                                ...inputStyle,
                                paddingRight: '120px',
                                borderBottomLeftRadius: '0',
                                borderBottomRightRadius: '0',
                                borderBottom: 'none'
                            }}
                        />
                        {/* Card Brand Icons */}
                        <div style={{
                            position: 'absolute',
                            right: '12px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            display: 'flex',
                            gap: '6px',
                            alignItems: 'center'
                        }}>
                            <div style={{
                                width: '24px',
                                height: '16px',
                                backgroundColor: '#1a1f71',
                                borderRadius: '2px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '8px',
                                color: 'white',
                                fontWeight: 'bold'
                            }}>VISA</div>
                            <div style={{
                                width: '24px',
                                height: '16px',
                                backgroundColor: '#eb001b',
                                borderRadius: '2px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '6px',
                                color: 'white',
                                fontWeight: 'bold'
                            }}>MC</div>
                            <div style={{
                                width: '24px',
                                height: '16px',
                                backgroundColor: '#006fcf',
                                borderRadius: '2px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '6px',
                                color: 'white',
                                fontWeight: 'bold'
                            }}>AMEX</div>
                        </div>
                    </div>

                    {/* Expiry and CVV Row */}
                    <div style={{
                        display: 'flex',
                        gap: '0'
                    }}>
                        <input
                            type="text"
                            value={expiryDate}
                            onChange={(e) => setExpiryDate(e.target.value)}
                            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                            placeholder="MM / YY"
                            required
                            style={{
                                ...inputStyle,
                                flex: 1,
                                borderTopLeftRadius: '0',
                                borderTopRightRadius: '0',
                                borderBottomRightRadius: '0',
                                borderRight: 'none'
                            }}
                        />
                        <input
                            type="text"
                            value={cvv}
                            onChange={(e) => setCvv(e.target.value)}
                            onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                            onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                            placeholder="CVC"
                            required
                            style={{
                                ...inputStyle,
                                flex: 1,
                                borderTopLeftRadius: '0',
                                borderTopRightRadius: '0',
                                borderBottomLeftRadius: '0'
                            }}
                        />
                    </div>
                </motion.div>

                {/* Name on Card */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="nameOnCard" style={labelStyle}>Name on card</label>
                    <input
                        type="text"
                        id="nameOnCard"
                        value={nameOnCard}
                        onChange={(e) => setNameOnCard(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                        placeholder="Full name on card"
                        required
                        style={inputStyle}
                    />
                </motion.div>

                {/* Country */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    style={{ marginBottom: '24px' }}
                >
                    <label htmlFor="country" style={labelStyle}>Country</label>
                    <select
                        id="country"
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                        style={{
                            ...inputStyle,
                            cursor: 'pointer',
                            backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 4 5\'><path fill=\'%23666\' d=\'M2 0L0 2h4zm0 5L0 3h4z\'/></svg>")',
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: 'right 12px center',
                            backgroundSize: '12px',
                            paddingRight: '40px'
                        }}
                    >
                        <option value="United States">United States</option>
                        <option value="Canada">Canada</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Australia">Australia</option>
                        <option value="Germany">Germany</option>
                        <option value="France">France</option>
                        <option value="Other">Other</option>
                    </select>
                </motion.div>
                <motion.button
                    type="submit"
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.7 }}
                    whileHover={{ scale: 1.01, backgroundColor: '#059669' }}
                    whileTap={{ scale: 0.99 }}
                    style={{
                        width: '100%',
                        padding: '16px',
                        borderRadius: '6px',
                        border: 'none',
                        backgroundColor: '#10b981',
                        color: '#ffffff',
                        fontWeight: '600',
                        fontSize: '16px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
                    }}
                >
                    Pay
                </motion.button>
            </motion.form>
        </motion.div>
    );
};

export default PaymentForm;
