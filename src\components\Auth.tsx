import React, { useState } from 'react';
import { motion } from 'framer-motion';

const PaymentForm: React.FC = () => {
    const [recipient, setRecipient] = useState('');
    const [amount, setAmount] = useState('');
    const [message, setMessage] = useState('');
    const [cardNumber, setCardNumber] = useState('');
    const [expiryDate, setExpiryDate] = useState('');
    const [cvv, setCvv] = useState('');

    const inputStyle = {
        width: '100%',
        padding: '12px',
        borderRadius: '8px',
        border: '2px solid #e0e0e0',
        transition: 'all 0.3s ease',
        outline: 'none',
        fontSize: '16px'
    };

    const labelStyle = {
        display: 'block',
        marginBottom: '8px',
        fontWeight: '600',
        color: '#2c3e50',
        fontSize: '14px'
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        console.log('Payment Details:', { recipient, amount, message, cardNumber, expiryDate, cvv });
        // Add logic to process the payment
    };

    return (
        <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            style={{
                maxWidth: '500px',
                margin: '0 auto',
                padding: '30px',
                textAlign: 'center',
                fontFamily: 'Arial, sans-serif',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
                borderRadius: '16px',
                backgroundColor: 'white',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.18)'
            }}>
            <motion.h1
                initial={{ y: -20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                style={{
                    color: '#333',
                    marginBottom: '30px',
                    fontSize: '2em',
                    background: 'linear-gradient(135deg, #4CAF50, #45a049)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                }}>
                Send Payment
            </motion.h1>
            <motion.form
                onSubmit={handleSubmit}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
            >
                <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="recipient" style={{
                        display: 'block',
                        marginBottom: '8px',
                        fontWeight: '600',
                        color: '#2c3e50'
                    }}>
                        Recipient
                    </label>
                    <input
                        type="text"
                        id="recipient"
                        value={recipient}
                        onChange={(e) => setRecipient(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#4CAF50'}
                        onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                        placeholder="Enter recipient's email or username"
                        required
                        style={{
                            width: '100%',
                            padding: '12px',
                            borderRadius: '8px',
                            border: '2px solid #e0e0e0',
                            transition: 'border-color 0.3s ease',
                            outline: 'none',
                            fontSize: '16px'
                        }}
                    />
                </motion.div>
                <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="amount" style={labelStyle}>Amount</label>
                    <input
                        type="number"
                        id="amount"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#4CAF50'}
                        onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                        placeholder="Enter amount"
                        required
                        style={inputStyle}
                    />
                </motion.div>

                <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="cardNumber" style={labelStyle}>Card Number</label>
                    <input
                        type="text"
                        id="cardNumber"
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#4CAF50'}
                        onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                        placeholder="1234 5678 9012 3456"
                        required
                        style={inputStyle}
                    />
                </motion.div>

                <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.7 }}
                    style={{
                        display: 'flex',
                        gap: '20px',
                        marginBottom: '20px'
                    }}
                >
                    <div style={{ flex: 1 }}>
                        <label htmlFor="expiryDate" style={labelStyle}>Expiry Date</label>
                        <input
                            type="text"
                            id="expiryDate"
                            value={expiryDate}
                            onChange={(e) => setExpiryDate(e.target.value)}
                            onFocus={(e) => e.target.style.borderColor = '#4CAF50'}
                            onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                            placeholder="MM/YY"
                            required
                            style={inputStyle}
                        />
                    </div>
                    <div style={{ flex: 1 }}>
                        <label htmlFor="cvv" style={labelStyle}>CVV</label>
                        <input
                            type="text"
                            id="cvv"
                            value={cvv}
                            onChange={(e) => setCvv(e.target.value)}
                            onFocus={(e) => e.target.style.borderColor = '#4CAF50'}
                            onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                            placeholder="123"
                            required
                            style={inputStyle}
                        />
                    </div>
                </motion.div>

                <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.8 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="message" style={labelStyle}>Message (Optional)</label>
                    <textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        onFocus={(e) => e.target.style.borderColor = '#4CAF50'}
                        onBlur={(e) => e.target.style.borderColor = '#e0e0e0'}
                        placeholder="Add a message"
                        style={{ ...inputStyle, minHeight: '100px', resize: 'vertical' }}
                    />
                </motion.div>
                <motion.button
                    type="submit"
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.9 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    style={{
                        width: '100%',
                        padding: '14px',
                        borderRadius: '8px',
                        border: 'none',
                        background: 'linear-gradient(135deg, #4CAF50, #45a049)',
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: '16px',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 6px rgba(76, 175, 80, 0.2)'
                    }}
                >
                    Send Payment
                </motion.button>
            </motion.form>
        </motion.div>
    );
};

export default PaymentForm;
