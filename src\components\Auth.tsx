import React, { useState } from 'react';
import { motion } from 'framer-motion';

const PaymentForm: React.FC = () => {
    const [email, setEmail] = useState('');
    const [amount, setAmount] = useState('');
    const [cardNumber, setCardNumber] = useState('');
    const [expiryDate, setExpiryDate] = useState('');
    const [cvv, setCvv] = useState('');
    const [nameOnCard, setNameOnCard] = useState('');
    const [country, setCountry] = useState('United States');
    const [isLoading, setIsLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);

    const inputStyle = {
        width: '100%',
        padding: '16px 18px',
        borderRadius: '8px',
        border: '2px solid #e5e7eb',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        outline: 'none',
        fontSize: '16px',
        backgroundColor: '#ffffff',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        boxSizing: 'border-box' as const,
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
    } as React.CSSProperties;

    const labelStyle = {
        display: 'block',
        marginBottom: '8px',
        fontWeight: '600',
        color: '#374151',
        fontSize: '15px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        letterSpacing: '0.025em'
    };

    // Input formatting functions
    const formatCardNumber = (value: string) => {
        // Remove all non-digits
        const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
        // Add space every 4 digits
        const matches = v.match(/\d{4,16}/g);
        const match = (matches && matches[0]) || '';
        const parts = [];
        for (let i = 0, len = match.length; i < len; i += 4) {
            parts.push(match.substring(i, i + 4));
        }
        if (parts.length) {
            return parts.join(' ');
        } else {
            return v;
        }
    };

    const formatExpiryDate = (value: string) => {
        // Remove all non-digits
        const v = value.replace(/\D/g, '');
        // Add slash after 2 digits
        if (v.length >= 2) {
            return v.substring(0, 2) + '/' + v.substring(2, 4);
        }
        return v;
    };

    const formatCVV = (value: string) => {
        // Only allow digits, max 4 characters
        return value.replace(/\D/g, '').substring(0, 4);
    };

    const formatAmount = (value: string) => {
        // Remove all non-digits and decimal points
        const v = value.replace(/[^0-9.]/g, '');
        // Ensure only one decimal point and max 2 decimal places
        const parts = v.split('.');
        if (parts.length > 2) {
            return parts[0] + '.' + parts.slice(1).join('');
        }
        if (parts[1] && parts[1].length > 2) {
            return parts[0] + '.' + parts[1].substring(0, 2);
        }
        return v;
    };

    const formatNameOnCard = (value: string) => {
        // Only allow letters, spaces, hyphens, and apostrophes
        return value.replace(/[^a-zA-Z\s\-']/g, '').substring(0, 50);
    };

    const detectCardType = (cardNumber: string) => {
        const number = cardNumber.replace(/\s/g, '');

        // Visa
        if (/^4/.test(number)) {
            return 'visa';
        }
        // Mastercard
        if (/^5[1-5]/.test(number) || /^2[2-7]/.test(number)) {
            return 'mastercard';
        }
        // American Express
        if (/^3[47]/.test(number)) {
            return 'amex';
        }
        // Discover
        if (/^6/.test(number)) {
            return 'discover';
        }

        return 'unknown';
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        // Log all user inputs before processing
        console.log('=== PAYMENT FORM SUBMISSION ===');
        console.log('Email:', email);
        console.log('Amount:', `$${amount}`);
        console.log('Card Number:', cardNumber.replace(/\s/g, '')); // Log without spaces
        console.log('Expiry Date:', expiryDate);
        console.log('CVV:', cvv);
        console.log('Name on Card:', nameOnCard);
        console.log('Country:', country);
        console.log('Timestamp:', new Date().toISOString());
        console.log('================================');

        // Start loading
        setIsLoading(true);

        // Simulate payment processing
        try {
            await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay
            setIsSuccess(true);
            console.log('Payment processed successfully!');
        } catch (error) {
            console.error('Payment failed:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Loading Screen Component
    if (isLoading) {
        return (
            <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3 }}
                style={{
                    maxWidth: '420px',
                    width: 'calc(100% - 32px)',
                    margin: '0 auto',
                    padding: 'clamp(32px, 6vw, 48px) clamp(24px, 5vw, 40px)',
                    textAlign: 'center',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    borderRadius: '16px',
                    backgroundColor: '#ffffff',
                    border: '1px solid #e5e7eb'
                }}
            >
                <motion.div
                    animate={{
                        rotate: 360,
                        scale: [1, 1.1, 1]
                    }}
                    transition={{
                        rotate: { duration: 2, repeat: Infinity, ease: "linear" },
                        scale: { duration: 1, repeat: Infinity, ease: "easeInOut" }
                    }}
                    style={{
                        width: '60px',
                        height: '60px',
                        margin: '0 auto 24px',
                        borderRadius: '50%',
                        border: '4px solid #f3f4f6',
                        borderTop: '4px solid #10b981',
                        borderRight: '4px solid #10b981'
                    }}
                />

                <motion.h2
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#111827',
                        margin: '0 0 8px 0'
                    }}
                >
                    Processing Payment
                </motion.h2>

                <motion.p
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        margin: '0 0 24px 0'
                    }}
                >
                    Please wait while we securely process your payment...
                </motion.p>

                <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 3, ease: "easeInOut" }}
                    style={{
                        height: '4px',
                        backgroundColor: '#10b981',
                        borderRadius: '2px',
                        marginTop: '16px'
                    }}
                />
            </motion.div>
        );
    }

    // Success Screen Component
    if (isSuccess) {
        return (
            <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, type: "spring" }}
                style={{
                    maxWidth: '420px',
                    width: 'calc(100% - 32px)',
                    margin: '0 auto',
                    padding: 'clamp(32px, 6vw, 48px) clamp(24px, 5vw, 40px)',
                    textAlign: 'center',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    borderRadius: '16px',
                    backgroundColor: '#ffffff',
                    border: '1px solid #e5e7eb'
                }}
            >
                <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    style={{
                        width: '80px',
                        height: '80px',
                        margin: '0 auto 24px',
                        borderRadius: '50%',
                        backgroundColor: '#10b981',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '36px'
                    }}
                >
                    ✓
                </motion.div>

                <motion.h2
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    style={{
                        fontSize: '24px',
                        fontWeight: '600',
                        color: '#111827',
                        margin: '0 0 8px 0'
                    }}
                >
                    Payment Successful!
                </motion.h2>

                <motion.p
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    style={{
                        fontSize: '16px',
                        color: '#6b7280',
                        margin: '0 0 24px 0'
                    }}
                >
                    Your payment of ${amount} has been processed successfully.
                </motion.p>

                <motion.button
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                        setIsSuccess(false);
                        setEmail('');
                        setAmount('');
                        setCardNumber('');
                        setExpiryDate('');
                        setCvv('');
                        setNameOnCard('');
                        setCountry('United States');
                    }}
                    style={{
                        padding: '12px 24px',
                        borderRadius: '6px',
                        border: 'none',
                        backgroundColor: '#10b981',
                        color: '#ffffff',
                        fontWeight: '600',
                        fontSize: '14px',
                        cursor: 'pointer',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                    }}
                >
                    Make Another Payment
                </motion.button>
            </motion.div>
        );
    }

    return (
        <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
            style={{
                maxWidth: '420px',
                width: 'calc(100% - 32px)',
                margin: '0 auto',
                padding: 'clamp(24px, 5vw, 40px)',
                textAlign: 'left',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                borderRadius: '16px',
                backgroundColor: '#ffffff',
                border: '1px solid #e5e7eb'
            }}>

            {/* Heading */}
            <motion.div
                initial={{ y: -10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.1 }}
                style={{
                    marginBottom: '32px',
                    textAlign: 'center'
                }}
            >
                <h1 style={{
                    fontSize: 'clamp(20px, 5vw, 28px)',
                    fontWeight: '700',
                    color: '#111827',
                    margin: '0 0 8px 0',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    letterSpacing: '-0.025em'
                }}>
                    Complete Payment
                </h1>
                <p style={{
                    fontSize: 'clamp(14px, 3.5vw, 16px)',
                    color: '#6b7280',
                    margin: '0',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    lineHeight: '1.5'
                }}>
                    Enter your payment details below
                </p>
            </motion.div>
            <motion.form
                onSubmit={handleSubmit}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
            >
                {/* Email Field */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="email" style={labelStyle}>
                        Email <span style={{ color: '#9ca3af', fontWeight: '400' }}>(optional)</span>
                    </label>
                    <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        onFocus={(e) => {
                            e.target.style.borderColor = '#3b82f6';
                            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                            e.target.style.transform = 'translateY(-1px)';
                        }}
                        onBlur={(e) => {
                            e.target.style.borderColor = '#e5e7eb';
                            e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                            e.target.style.transform = 'translateY(0)';
                        }}
                        placeholder="<EMAIL>"
                        autoComplete="email"
                        style={inputStyle}
                    />
                </motion.div>

                {/* Amount Field */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="amount" style={labelStyle}>
                        Amount
                    </label>
                    <div style={{ position: 'relative' }}>
                        <span style={{
                            position: 'absolute',
                            left: '16px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            color: '#6b7280',
                            fontSize: '16px',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            $
                        </span>
                        <input
                            type="text"
                            id="amount"
                            value={amount}
                            onChange={(e) => setAmount(formatAmount(e.target.value))}
                            onFocus={(e) => {
                                e.target.style.borderColor = '#3b82f6';
                                e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                                e.target.style.transform = 'translateY(0)';
                            }}
                            placeholder="0.00"
                            required
                            inputMode="decimal"
                            style={{
                                ...inputStyle,
                                paddingLeft: 'clamp(36px, 8vw, 42px)'
                            }}
                        />
                    </div>
                </motion.div>
                {/* Card Information Section */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="cardNumber" style={labelStyle}>Card information</label>
                    <div style={{ position: 'relative' }}>
                        <input
                            type="text"
                            id="cardNumber"
                            value={cardNumber}
                            onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                            onFocus={(e) => {
                                e.target.style.borderColor = '#3b82f6';
                                e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                                e.target.style.transform = 'translateY(0)';
                            }}
                            placeholder="1234 1234 1234 1234"
                            required
                            maxLength={19}
                            inputMode="numeric"
                            autoComplete="cc-number"
                            style={{
                                ...inputStyle,
                                paddingRight: 'clamp(100px, 25vw, 130px)',
                                borderBottomLeftRadius: '0',
                                borderBottomRightRadius: '0',
                                borderBottom: 'none'
                            }}
                        />
                        {/* Card Brand Icons */}
                        <div style={{
                            position: 'absolute',
                            right: 'clamp(12px, 3vw, 16px)',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            display: 'flex',
                            gap: 'clamp(4px, 1vw, 6px)',
                            alignItems: 'center'
                        }}>
                            {(() => {
                                const cardType = detectCardType(cardNumber);
                                const cardIcons = {
                                    visa: { bg: '#1a1f71', text: 'VISA', size: '7px' },
                                    mastercard: { bg: '#eb001b', text: 'MC', size: '6px' },
                                    amex: { bg: '#006fcf', text: 'AMEX', size: '5px' },
                                    discover: { bg: '#ff6000', text: 'DISC', size: '5px' },
                                    unknown: { bg: '#6b7280', text: 'CARD', size: '5px' }
                                };

                                if (cardType !== 'unknown') {
                                    const icon = cardIcons[cardType];
                                    return (
                                        <div style={{
                                            width: '32px',
                                            height: '20px',
                                            backgroundColor: icon.bg,
                                            borderRadius: '3px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            fontSize: icon.size,
                                            color: 'white',
                                            fontWeight: 'bold',
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
                                        }}>
                                            {icon.text}
                                        </div>
                                    );
                                } else {
                                    return (
                                        <>
                                            <div style={{
                                                width: '24px',
                                                height: '16px',
                                                backgroundColor: '#e5e7eb',
                                                borderRadius: '2px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '7px',
                                                color: '#6b7280',
                                                fontWeight: 'bold'
                                            }}>VISA</div>
                                            <div style={{
                                                width: '24px',
                                                height: '16px',
                                                backgroundColor: '#e5e7eb',
                                                borderRadius: '2px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '6px',
                                                color: '#6b7280',
                                                fontWeight: 'bold'
                                            }}>MC</div>
                                            <div style={{
                                                width: '24px',
                                                height: '16px',
                                                backgroundColor: '#e5e7eb',
                                                borderRadius: '2px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '5px',
                                                color: '#6b7280',
                                                fontWeight: 'bold'
                                            }}>AMEX</div>
                                        </>
                                    );
                                }
                            })()}
                        </div>
                    </div>

                    {/* Expiry and CVV Row */}
                    <div style={{
                        display: 'flex',
                        gap: '0'
                    }}>
                        <input
                            type="text"
                            value={expiryDate}
                            onChange={(e) => setExpiryDate(formatExpiryDate(e.target.value))}
                            onFocus={(e) => {
                                e.target.style.borderColor = '#3b82f6';
                                e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                                e.target.style.transform = 'translateY(0)';
                            }}
                            placeholder="MM/YY"
                            title="Enter expiry date in MM/YY format"
                            required
                            maxLength={5}
                            inputMode="numeric"
                            autoComplete="cc-exp"
                            style={{
                                ...inputStyle,
                                flex: 1,
                                borderTopLeftRadius: '0',
                                borderTopRightRadius: '0',
                                borderBottomRightRadius: '0',
                                borderRight: 'none'
                            }}
                        />
                        <input
                            type="text"
                            value={cvv}
                            onChange={(e) => setCvv(formatCVV(e.target.value))}
                            onFocus={(e) => {
                                e.target.style.borderColor = '#3b82f6';
                                e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                                e.target.style.transform = 'translateY(-1px)';
                            }}
                            onBlur={(e) => {
                                e.target.style.borderColor = '#e5e7eb';
                                e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                                e.target.style.transform = 'translateY(0)';
                            }}
                            placeholder="CVC"
                            title="3-4 digit security code on back of card"
                            required
                            maxLength={4}
                            inputMode="numeric"
                            autoComplete="cc-csc"
                            style={{
                                ...inputStyle,
                                flex: 1,
                                borderTopLeftRadius: '0',
                                borderTopRightRadius: '0',
                                borderBottomLeftRadius: '0'
                            }}
                        />
                    </div>
                </motion.div>

                {/* Name on Card */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    style={{ marginBottom: '20px' }}
                >
                    <label htmlFor="nameOnCard" style={labelStyle}>Name on card</label>
                    <input
                        type="text"
                        id="nameOnCard"
                        value={nameOnCard}
                        onChange={(e) => setNameOnCard(formatNameOnCard(e.target.value))}
                        onFocus={(e) => {
                            e.target.style.borderColor = '#3b82f6';
                            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                            e.target.style.transform = 'translateY(-1px)';
                        }}
                        onBlur={(e) => {
                            e.target.style.borderColor = '#e5e7eb';
                            e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                            e.target.style.transform = 'translateY(0)';
                        }}
                        placeholder="Full name on card"
                        required
                        autoComplete="cc-name"
                        style={inputStyle}
                    />
                </motion.div>

                {/* Country */}
                <motion.div
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    style={{ marginBottom: '24px' }}
                >
                    <label htmlFor="country" style={labelStyle}>Country</label>
                    <select
                        id="country"
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                        onFocus={(e) => {
                            e.target.style.borderColor = '#3b82f6';
                            e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                            e.target.style.transform = 'translateY(-1px)';
                        }}
                        onBlur={(e) => {
                            e.target.style.borderColor = '#e5e7eb';
                            e.target.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
                            e.target.style.transform = 'translateY(0)';
                        }}
                        style={{
                            ...inputStyle,
                            cursor: 'pointer',
                            backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 4 5\'><path fill=\'%23666\' d=\'M2 0L0 2h4zm0 5L0 3h4z\'/></svg>")',
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: 'right clamp(12px, 3vw, 16px) center',
                            backgroundSize: 'clamp(10px, 2.5vw, 12px)',
                            paddingRight: 'clamp(36px, 8vw, 44px)'
                        }}
                    >
                        <option value="United States">United States</option>
                        <option value="Canada">Canada</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Australia">Australia</option>
                        <option value="Germany">Germany</option>
                        <option value="France">France</option>
                        <option value="Other">Other</option>
                    </select>
                </motion.div>
                <motion.button
                    type="submit"
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.7 }}
                    whileHover={!isLoading ? { scale: 1.01, backgroundColor: '#059669' } : {}}
                    whileTap={!isLoading ? { scale: 0.99 } : {}}
                    disabled={isLoading}
                    style={{
                        width: '100%',
                        padding: '16px',
                        borderRadius: '6px',
                        border: 'none',
                        backgroundColor: isLoading ? '#9ca3af' : '#10b981',
                        color: '#ffffff',
                        fontWeight: '600',
                        fontSize: '16px',
                        cursor: isLoading ? 'not-allowed' : 'pointer',
                        transition: 'all 0.2s ease',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px'
                    }}
                >
                    {isLoading && (
                        <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            style={{
                                width: '16px',
                                height: '16px',
                                border: '2px solid #ffffff',
                                borderTop: '2px solid transparent',
                                borderRadius: '50%'
                            }}
                        />
                    )}
                    {isLoading ? 'Processing...' : 'Pay'}
                </motion.button>
            </motion.form>
        </motion.div>
    );
};

export default PaymentForm;
