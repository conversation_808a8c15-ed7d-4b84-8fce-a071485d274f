import React from 'react';

const TransactionHistory: React.FC = () => {
    const transactions = [
        { id: 1, description: 'Payment to <PERSON>', amount: -200, date: '2025-08-10', status: 'Completed' },
        { id: 2, description: 'Payment from <PERSON>', amount: 300, date: '2025-08-09', status: 'Completed' },
        { id: 3, description: 'Payment to Grocery Store', amount: -50, date: '2025-08-08', status: 'Pending' },
    ];

    return (
        <div style={{ padding: '20px' }}>
            <h1>Transaction History</h1>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                    <tr>
                        <th style={{ border: '1px solid #ddd', padding: '8px' }}>Date</th>
                        <th style={{ border: '1px solid #ddd', padding: '8px' }}>Description</th>
                        <th style={{ border: '1px solid #ddd', padding: '8px' }}>Amount</th>
                        <th style={{ border: '1px solid #ddd', padding: '8px' }}>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {transactions.map((transaction) => (
                        <tr key={transaction.id}>
                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{transaction.date}</td>
                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{transaction.description}</td>
                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>${transaction.amount}</td>
                            <td style={{ border: '1px solid #ddd', padding: '8px' }}>{transaction.status}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default TransactionHistory;
