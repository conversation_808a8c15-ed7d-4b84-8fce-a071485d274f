import React from 'react';
import { motion } from 'framer-motion';

interface PaymentData {
    id: string;
    email: string;
    amount: string;
    currency: string;
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    nameOnCard: string;
    country: string;
    timestamp: string;
    formattedAmount: string;
}

interface PaymentDetailsProps {
    payment: PaymentData;
    onClose: () => void;
}

const PaymentDetails: React.FC<PaymentDetailsProps> = ({ payment, onClose }) => {
    const formatDate = (timestamp: string) => {
        const date = new Date(timestamp);
        return {
            date: date.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            }),
            time: date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            })
        };
    };

    const getCardBrand = (cardNumber: string) => {
        const number = cardNumber.replace(/\s/g, '');
        if (/^4/.test(number)) return { name: 'Visa', color: '#1a1f71' };
        if (/^5[1-5]/.test(number) || /^2[2-7]/.test(number)) return { name: 'Mastercard', color: '#eb001b' };
        if (/^3[47]/.test(number)) return { name: 'American Express', color: '#006fcf' };
        if (/^6/.test(number)) return { name: 'Discover', color: '#ff6000' };
        return { name: 'Unknown', color: '#6b7280' };
    };

    const getCurrencySymbol = (currencyCode: string) => {
        const symbols: { [key: string]: string } = {
            'USD': '$', 'EUR': '€', 'GBP': '£', 'JPY': '¥', 'CAD': 'C$',
            'AUD': 'A$', 'CHF': 'CHF', 'CNY': '¥', 'INR': '₹'
        };
        return symbols[currencyCode] || currencyCode;
    };

    const { date, time } = formatDate(payment.timestamp);
    const cardBrand = getCardBrand(payment.cardNumber);

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '20px',
                zIndex: 1000
            }}
            onClick={onClose}
        >
            <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                style={{
                    backgroundColor: '#ffffff',
                    borderRadius: '16px',
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                    border: '1px solid #e5e7eb',
                    width: '100%',
                    maxWidth: '600px',
                    maxHeight: '90vh',
                    overflow: 'auto'
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <div style={{
                    padding: '24px',
                    borderBottom: '1px solid #e5e7eb',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}>
                    <div>
                        <h2 style={{
                            fontSize: '20px',
                            fontWeight: '700',
                            color: '#111827',
                            margin: '0 0 4px 0',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            💳 Payment Details
                        </h2>
                        <p style={{
                            fontSize: '14px',
                            color: '#6b7280',
                            margin: '0',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            Transaction ID: {payment.id}
                        </p>
                    </div>
                    <button
                        onClick={onClose}
                        style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '50%',
                            border: 'none',
                            backgroundColor: '#f3f4f6',
                            color: '#6b7280',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '16px',
                            transition: 'all 0.2s ease'
                        }}
                        onMouseEnter={(e) => {
                            (e.target as HTMLButtonElement).style.backgroundColor = '#e5e7eb';
                            (e.target as HTMLButtonElement).style.color = '#374151';
                        }}
                        onMouseLeave={(e) => {
                            (e.target as HTMLButtonElement).style.backgroundColor = '#f3f4f6';
                            (e.target as HTMLButtonElement).style.color = '#6b7280';
                        }}
                    >
                        ✕
                    </button>
                </div>

                {/* Content */}
                <div style={{ padding: '24px' }}>
                    {/* Amount Section */}
                    <div style={{
                        backgroundColor: '#f0fdf4',
                        border: '1px solid #bbf7d0',
                        borderRadius: '12px',
                        padding: '20px',
                        marginBottom: '24px',
                        textAlign: 'center'
                    }}>
                        <div style={{
                            fontSize: '36px',
                            fontWeight: '700',
                            color: '#10b981',
                            margin: '0 0 8px 0',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            {payment.formattedAmount}
                        </div>
                        <div style={{
                            fontSize: '14px',
                            color: '#059669',
                            fontWeight: '600',
                            textTransform: 'uppercase',
                            letterSpacing: '0.05em'
                        }}>
                            Payment Amount
                        </div>
                    </div>

                    {/* Payment Information Grid */}
                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                        gap: '20px',
                        marginBottom: '24px'
                    }}>
                        {/* Customer Information */}
                        <div style={{
                            backgroundColor: '#f9fafb',
                            borderRadius: '12px',
                            padding: '20px'
                        }}>
                            <h3 style={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#111827',
                                margin: '0 0 16px 0',
                                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            }}>
                                👤 Customer Information
                            </h3>
                            <div style={{ marginBottom: '12px' }}>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Name on Card
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {payment.nameOnCard}
                                </div>
                            </div>
                            <div style={{ marginBottom: '12px' }}>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Email Address
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {payment.email || 'Not provided'}
                                </div>
                            </div>
                            <div>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Country
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {payment.country}
                                </div>
                            </div>
                        </div>

                        {/* Payment Method */}
                        <div style={{
                            backgroundColor: '#f9fafb',
                            borderRadius: '12px',
                            padding: '20px'
                        }}>
                            <h3 style={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#111827',
                                margin: '0 0 16px 0',
                                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                            }}>
                                💳 Payment Method
                            </h3>
                            <div style={{ marginBottom: '12px' }}>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Card Number
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px'
                                }}>
                                    {payment.cardNumber}
                                    <span style={{
                                        backgroundColor: cardBrand.color,
                                        color: 'white',
                                        padding: '2px 6px',
                                        borderRadius: '4px',
                                        fontSize: '10px',
                                        fontWeight: '600'
                                    }}>
                                        {cardBrand.name}
                                    </span>
                                </div>
                            </div>
                            <div style={{ marginBottom: '12px' }}>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Expiry Date
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {payment.expiryDate}
                                </div>
                            </div>
                            <div>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    CVV
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    ***
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Transaction Details */}
                    <div style={{
                        backgroundColor: '#f9fafb',
                        borderRadius: '12px',
                        padding: '20px'
                    }}>
                        <h3 style={{
                            fontSize: '16px',
                            fontWeight: '600',
                            color: '#111827',
                            margin: '0 0 16px 0',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
                        }}>
                            📊 Transaction Details
                        </h3>
                        <div style={{
                            display: 'grid',
                            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                            gap: '16px'
                        }}>
                            <div>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Transaction Date
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {date}
                                </div>
                            </div>
                            <div>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Transaction Time
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {time}
                                </div>
                            </div>
                            <div>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Currency
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#111827',
                                    fontWeight: '500'
                                }}>
                                    {payment.currency} ({getCurrencySymbol(payment.currency)})
                                </div>
                            </div>
                            <div>
                                <div style={{
                                    fontSize: '12px',
                                    color: '#6b7280',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.05em',
                                    marginBottom: '4px'
                                }}>
                                    Status
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: '#10b981',
                                    fontWeight: '600',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '4px'
                                }}>
                                    <div style={{
                                        width: '8px',
                                        height: '8px',
                                        borderRadius: '50%',
                                        backgroundColor: '#10b981'
                                    }} />
                                    Completed
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>
        </motion.div>
    );
};

export default PaymentDetails;
